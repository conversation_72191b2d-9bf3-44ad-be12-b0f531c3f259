const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Testing Next.js configuration...');

// Clean .next directory
const nextDir = path.join(__dirname, '.next');
if (fs.existsSync(nextDir)) {
  console.log('Cleaning .next directory...');
  fs.rmSync(nextDir, { recursive: true, force: true });
}

try {
  console.log('Running Next.js build...');
  execSync('npx next build', { 
    stdio: 'inherit',
    cwd: __dirname 
  });
  console.log('Build successful!');
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
}
