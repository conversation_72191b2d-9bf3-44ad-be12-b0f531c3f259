# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js
.next/
out/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/settings.json
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Turbo
.turbo

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Test files and temporary scripts
test-build.js
*.tmp
*.temp

# Cache directories
.cache/
.parcel-cache/

# Storybook build outputs
storybook-static

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Production builds
*.tgz
*.tar.gz

# Optional REPL history
.node_repl_history

# Yarn Integrity file
.yarn-integrity
